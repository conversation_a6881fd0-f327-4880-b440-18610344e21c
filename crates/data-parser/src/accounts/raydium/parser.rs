//! Raydium 账号数据 Anchor 解析器实现
//!
//! 使用通用基础结构和宏来减少重复代码

use crate::accounts::traits::AccountType;
use crate::anchor_types::raydium::{
    RAYDIUM_CLMM_PROGRAM_ID, PoolState, TickArrayState, TickArrayBitmapExtension,
    ObservationState, PersonalPositionState, ProtocolPositionState
};
use crate::{define_anchor_parser, impl_account_data_adapter, impl_parse_function};

// 使用宏定义完整的 Raydium 解析器
define_anchor_parser! {
    parser: RaydiumAnchorAccountParser,
    program_id: RAYDIUM_CLMM_PROGRAM_ID,
    types: {
        AccountType::RaydiumPoolState => {
            discriminator: [247, 237, 227, 245, 215, 195, 222, 70],
            anchor_type: PoolState,
            adapter: RaydiumPoolStateAdapter,
            parse_fn: parse_pool_state,
            data_size: 1544,
            json_fields: {
                bump: inner.bump,
                amm_config: inner.amm_config.to_string(),
                owner: inner.owner.to_string(),
                token_mint_0: inner.token_mint_0.to_string(),
                token_mint_1: inner.token_mint_1.to_string(),
                token_vault_0: inner.token_vault_0.to_string(),
                token_vault_1: inner.token_vault_1.to_string(),
                observation_key: inner.observation_key.to_string(),
                mint_decimals_0: inner.mint_decimals_0,
                mint_decimals_1: inner.mint_decimals_1,
                tick_spacing: inner.tick_spacing,
                liquidity: inner.liquidity.to_string(),
                sqrt_price_x64: inner.sqrt_price_x64.to_string(),
                tick_current: inner.tick_current,
                status: inner.status
            },
            creator: inner.owner,
            status: inner.status,
            mints: [inner.token_mint_0, inner.token_mint_1]
        },
        AccountType::RaydiumTickArrayState => {
            discriminator: [192, 155, 85, 205, 49, 249, 129, 42],
            anchor_type: TickArrayState,
            adapter: RaydiumTickArrayStateAdapter,
            parse_fn: parse_tick_array_state,
            data_size: 32 + 4 + 60 * 136 + 1 + 8 + 107,
            json_fields: {
                pool_id: inner.pool_id.to_string(),
                start_tick_index: inner.start_tick_index,
                initialized_tick_count: inner.initialized_tick_count,
                recent_epoch: inner.recent_epoch
            }
        },
        AccountType::RaydiumTickArrayBitmapExtension => {
            discriminator: [60, 150, 36, 219, 97, 128, 139, 153],
            anchor_type: TickArrayBitmapExtension,
            adapter: RaydiumTickArrayBitmapExtensionAdapter,
            parse_fn: parse_tick_array_bitmap_extension,
            data_size: 32 + 32 + 1024 * 8 + 1024 * 8,
            json_fields: {
                pool_id: inner.pool_id.to_string()
            }
        },
        AccountType::RaydiumObservationState => {
            discriminator: [122, 174, 197, 53, 129, 9, 165, 132],
            anchor_type: ObservationState,
            adapter: RaydiumObservationStateAdapter,
            parse_fn: parse_observation_state,
            data_size: 1 + 8 + 2 + 32 + 100 * 44 + 32,
            json_fields: {
                initialized: inner.initialized,
                recent_epoch: inner.recent_epoch,
                observation_index: inner.observation_index,
                pool_id: inner.pool_id.to_string()
            }
        },
        AccountType::RaydiumPersonalPosition => {
            discriminator: [70, 111, 150, 126, 230, 15, 25, 117],
            anchor_type: PersonalPositionState,
            adapter: RaydiumPersonalPositionAdapter,
            parse_fn: parse_personal_position_state,
            data_size: 1 + 32 + 32 + 4 + 4 + 16 + 16 + 16 + 8 + 8 + 48 + 24,
            json_fields: {
                bump: inner.bump,
                nft_mint: inner.nft_mint.to_string(),
                pool_id: inner.pool_id.to_string(),
                tick_lower_index: inner.tick_lower_index,
                tick_upper_index: inner.tick_upper_index,
                liquidity: inner.liquidity.to_string()
            }
        },
        AccountType::RaydiumProtocolPosition => {
            discriminator: [100, 226, 145, 99, 146, 218, 160, 106],
            anchor_type: ProtocolPositionState,
            adapter: RaydiumProtocolPositionAdapter,
            parse_fn: parse_protocol_position_state,
            data_size: 1 + 32 + 4 + 4 + 16 + 16 + 16 + 8 + 8 + 48 + 24,
            json_fields: {
                bump: inner.bump,
                pool_id: inner.pool_id.to_string(),
                tick_lower_index: inner.tick_lower_index,
                tick_upper_index: inner.tick_upper_index,
                liquidity: inner.liquidity.to_string()
            }
        }
    }
}


