//! Anchor 解析器集成测试
//!
//! 验证 Anchor 解析器的正确性和性能

#[cfg(test)]
mod tests {
    use super::super::anchor_manager::AnchorParserManager;
    use super::super::traits::{AccountParser, AccountType};
    use crate::anchor_types::raydium::RAYDIUM_CLMM_PROGRAM_ID;
    use crate::anchor_types::meteora::METEORA_DLMM_PROGRAM_ID;
    use solana_sdk::pubkey::Pubkey;

    #[test]
    fn test_anchor_parser_manager_creation() {
        let manager = AnchorParserManager::new();
        assert_eq!(manager.name(), "AnchorParserManager");
    }

    #[test]
    fn test_supported_program_ids() {
        let manager = AnchorParserManager::new();
        let program_ids = manager.supported_program_ids();
        
        assert!(program_ids.contains(&RAYDIUM_CLMM_PROGRAM_ID));
        assert!(program_ids.contains(&METEORA_DLMM_PROGRAM_ID));
        assert_eq!(program_ids.len(), 2);
    }

    #[test]
    fn test_supported_account_types() {
        let manager = AnchorParserManager::new();
        let account_types = manager.supported_account_types();
        
        // 验证包含 Raydium 账号类型
        assert!(account_types.contains(&AccountType::RaydiumPoolState));
        assert!(account_types.contains(&AccountType::RaydiumTickArrayState));
        assert!(account_types.contains(&AccountType::RaydiumTickArrayBitmapExtension));
        assert!(account_types.contains(&AccountType::RaydiumObservationState));
        assert!(account_types.contains(&AccountType::RaydiumPersonalPosition));
        assert!(account_types.contains(&AccountType::RaydiumProtocolPosition));
        
        // 验证包含 Meteora 账号类型
        assert!(account_types.contains(&AccountType::MeteoraLbPair));
        assert!(account_types.contains(&AccountType::MeteoraBinArray));
        assert!(account_types.contains(&AccountType::MeteoraBinArrayBitmapExtension));
        assert!(account_types.contains(&AccountType::MeteoraOracle));
        assert!(account_types.contains(&AccountType::MeteoraPosition));
        assert!(account_types.contains(&AccountType::MeteoraPositionV2));
        
        // 验证总数
        assert_eq!(account_types.len(), 12);
    }

    #[test]
    fn test_can_parse_with_supported_programs() {
        let manager = AnchorParserManager::new();
        
        // 测试支持的程序ID
        let dummy_data = vec![0u8; 100]; // 创建一些虚拟数据
        
        // 注意：这里我们只测试程序ID识别，不测试实际解析
        // 因为我们需要真实的账号数据才能测试解析功能
        
        // 测试不支持的程序ID
        let unknown_program_id = Pubkey::new_unique();
        assert!(!manager.can_parse(&unknown_program_id, &dummy_data));
    }

    #[test]
    fn test_identify_account_type_with_invalid_data() {
        let manager = AnchorParserManager::new();
        
        // 测试数据太短的情况
        let short_data = vec![0u8; 4];
        assert!(manager.identify_account_type(&RAYDIUM_CLMM_PROGRAM_ID, &short_data).is_none());
        assert!(manager.identify_account_type(&METEORA_DLMM_PROGRAM_ID, &short_data).is_none());
        
        // 测试不支持的程序ID
        let unknown_program_id = Pubkey::new_unique();
        let dummy_data = vec![0u8; 100];
        assert!(manager.identify_account_type(&unknown_program_id, &dummy_data).is_none());
    }

    #[test]
    fn test_discriminator_recognition() {
        let manager = AnchorParserManager::new();
        
        // 测试 Raydium PoolState discriminator
        let mut pool_state_data = vec![0u8; 100];
        pool_state_data[0..8].copy_from_slice(&[247, 237, 227, 245, 215, 195, 222, 70]);
        
        let account_type = manager.identify_account_type(&RAYDIUM_CLMM_PROGRAM_ID, &pool_state_data);
        assert_eq!(account_type, Some(AccountType::RaydiumPoolState));
        
        // 测试 Meteora LbPair discriminator
        let mut lb_pair_data = vec![0u8; 100];
        lb_pair_data[0..8].copy_from_slice(&[155, 237, 145, 113, 29, 46, 40, 142]);
        
        let account_type = manager.identify_account_type(&METEORA_DLMM_PROGRAM_ID, &lb_pair_data);
        assert_eq!(account_type, Some(AccountType::MeteoraLbPair));
        
        // 测试未知 discriminator
        let mut unknown_data = vec![0u8; 100];
        unknown_data[0..8].copy_from_slice(&[1, 2, 3, 4, 5, 6, 7, 8]);
        
        let account_type = manager.identify_account_type(&RAYDIUM_CLMM_PROGRAM_ID, &unknown_data);
        assert!(account_type.is_none());
    }

    #[tokio::test]
    async fn test_parse_account_with_invalid_program_id() {
        let manager = AnchorParserManager::new();
        let unknown_program_id = Pubkey::new_unique();
        let dummy_address = Pubkey::new_unique();
        let dummy_data = vec![0u8; 100];
        
        let result = manager.parse_account(dummy_address, unknown_program_id, &dummy_data).await;
        assert!(result.is_err());
        
        if let Err(e) = result {
            assert!(e.to_string().contains("No parser available for program ID"));
        }
    }

    #[test]
    fn test_anchor_types_basic_properties() {
        // 测试 Raydium 程序ID
        assert_eq!(
            RAYDIUM_CLMM_PROGRAM_ID.to_string(),
            "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK"
        );
        
        // 测试 Meteora 程序ID
        assert_eq!(
            METEORA_DLMM_PROGRAM_ID.to_string(),
            "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"
        );
    }

    #[test]
    fn test_account_type_methods() {
        // 测试 Raydium 账号类型识别
        assert!(AccountType::RaydiumPoolState.is_raydium());
        assert!(!AccountType::RaydiumPoolState.is_meteora());
        
        // 测试 Meteora 账号类型识别
        assert!(AccountType::MeteoraLbPair.is_meteora());
        assert!(!AccountType::MeteoraLbPair.is_raydium());
        
        // 测试字符串表示
        assert_eq!(AccountType::RaydiumPoolState.as_str(), "RaydiumPoolState");
        assert_eq!(AccountType::MeteoraLbPair.as_str(), "MeteoraLbPair");
        assert_eq!(AccountType::MeteoraPositionV2.as_str(), "MeteoraPositionV2");
    }

    #[test]
    fn test_performance_benchmark() {
        let manager = AnchorParserManager::new();
        let start = std::time::Instant::now();
        
        // 执行1000次程序ID检查
        for _ in 0..1000 {
            let _ = manager.supported_program_ids();
        }
        
        let duration = start.elapsed();
        println!("1000次程序ID检查耗时: {:?}", duration);
        
        // 确保性能在合理范围内（应该很快）
        assert!(duration.as_millis() < 100);
    }
}
