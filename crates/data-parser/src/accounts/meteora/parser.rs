//! Meteora 账号数据 Anchor 解析器实现
//!
//! 使用通用基础结构和宏来减少重复代码

use crate::accounts::traits::AccountType;
use crate::anchor_types::meteora::{
    METEORA_DLMM_PROGRAM_ID, LbPair, BinArray, BinArrayBitmapExtension,
    Oracle, Position, PositionV2
};
use crate::{define_anchor_parser, impl_account_data_adapter, impl_parse_function};

// 使用宏定义完整的 Meteora 解析器
define_anchor_parser! {
    parser: MeteoraAnchorAccountParser,
    program_id: METEORA_DLMM_PROGRAM_ID,
    types: {
        AccountType::MeteoraLbPair => {
            discriminator: [155, 237, 145, 113, 29, 46, 40, 142],
            anchor_type: Lb<PERSON><PERSON>,
            adapter: MeteoraLbPairAdapter,
            parse_fn: parse_lb_pair,
            data_size: 1544,
            json_fields: {
                active_id: inner.active_id,
                bin_step: inner.bin_step,
                status: inner.status,
                token_x_mint: inner.token_x_mint.to_string(),
                token_y_mint: inner.token_y_mint.to_string(),
                reserve_x: inner.reserve_x.to_string(),
                reserve_y: inner.reserve_y.to_string(),
                protocol_fee_x: inner.protocol_fee_x,
                protocol_fee_y: inner.protocol_fee_y,
                fee_owner: inner.fee_owner.to_string(),
                oracle: inner.oracle.to_string(),
                last_updated_at: inner.last_updated_at,
                creator: inner.creator.to_string()
            },
            creator: inner.creator,
            status: inner.status,
            mints: [inner.token_x_mint, inner.token_y_mint]
        },
        AccountType::MeteoraBinArray => {
            discriminator: [119, 54, 29, 12, 13, 12, 48, 179],
            anchor_type: BinArray,
            adapter: MeteoraBinArrayAdapter,
            parse_fn: parse_bin_array,
            data_size: 8 + 1 + 7 + 32 + 70 * 128,
            json_fields: {
                index: inner.index,
                version: inner.version,
                lb_pair: inner.lb_pair.to_string()
            }
        },
        AccountType::MeteoraBinArrayBitmapExtension => {
            discriminator: [133, 242, 94, 158, 218, 114, 218, 103],
            anchor_type: BinArrayBitmapExtension,
            adapter: MeteoraBinArrayBitmapExtensionAdapter,
            parse_fn: parse_bin_array_bitmap_extension,
            data_size: 32 + 12 * 8 * 8 + 12 * 8 * 8,
            json_fields: {
                lb_pair: inner.lb_pair.to_string()
            }
        },
        AccountType::MeteoraOracle => {
            discriminator: [217, 230, 65, 101, 201, 162, 27, 125],
            anchor_type: Oracle,
            adapter: MeteoraOracleAdapter,
            parse_fn: parse_oracle,
            data_size: 8 + 8 + 8,
            json_fields: {
                idx: inner.idx,
                active_size: inner.active_size,
                length: inner.length
            }
        },
        AccountType::MeteoraPosition => {
            discriminator: [170, 188, 143, 228, 122, 64, 247, 208],
            anchor_type: Position,
            adapter: MeteoraPositionAdapter,
            parse_fn: parse_position,
            data_size: 32 + 32 + 70 * 8 + 70 * 24 + 70 * 32 + 4 + 4 + 8 + 32 + 8 + 1 + 87,
            json_fields: {
                lb_pair: inner.lb_pair.to_string(),
                owner: inner.owner.to_string(),
                lower_bin_id: inner.lower_bin_id,
                upper_bin_id: inner.upper_bin_id,
                last_updated_at: inner.last_updated_at,
                operator: inner.operator.to_string(),
                lock_release_point: inner.lock_release_point,
                subject_to_base: inner.subject_to_base
            },
            creator: inner.owner
        },
        AccountType::MeteoraPositionV2 => {
            discriminator: [171, 188, 143, 228, 122, 64, 247, 208],
            anchor_type: PositionV2,
            adapter: MeteoraPositionV2Adapter,
            parse_fn: parse_position_v2,
            data_size: 32 + 32 + 70 * 16 + 70 * 24 + 70 * 32 + 4 + 4 + 8 + 32 + 8 + 1 + 87,
            json_fields: {
                lb_pair: inner.lb_pair.to_string(),
                owner: inner.owner.to_string(),
                lower_bin_id: inner.lower_bin_id,
                upper_bin_id: inner.upper_bin_id,
                last_updated_at: inner.last_updated_at,
                operator: inner.operator.to_string(),
                lock_release_point: inner.lock_release_point,
                subject_to_base: inner.subject_to_base
            },
            creator: inner.owner
        }
    }
}


