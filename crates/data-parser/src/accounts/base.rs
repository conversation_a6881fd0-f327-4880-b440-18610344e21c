//! 通用 Anchor 解析器基础结构
//!
//! 提供可复用的解析器基础功能，减少重复代码

use async_trait::async_trait;
use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::collections::HashMap;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount, AccountData
};

/// Discriminator 映射类型
pub type DiscriminatorMap = HashMap<[u8; 8], AccountType>;

/// 解析函数类型
pub type ParseFunction = fn(&BaseAnchorParser, Pubkey, &[u8]) -> Result<Box<dyn AccountData>>;

/// 通用 Anchor 解析器基础结构
pub struct BaseAnchorParser {
    /// 解析器名称
    pub name: String,
    /// 支持的程序ID
    pub program_id: Pubkey,
    /// Discriminator 到账号类型的映射
    pub discriminator_map: DiscriminatorMap,
    /// 账号类型到解析函数的映射
    pub parse_functions: HashMap<AccountType, ParseFunction>,
    /// 支持的账号类型列表
    pub supported_types: Vec<AccountType>,
}

impl BaseAnchorParser {
    /// 创建新的基础解析器
    pub fn new(
        name: String,
        program_id: Pubkey,
        discriminator_map: DiscriminatorMap,
        parse_functions: HashMap<AccountType, ParseFunction>,
    ) -> Self {
        let supported_types = discriminator_map.values().cloned().collect();
        
        Self {
            name,
            program_id,
            discriminator_map,
            parse_functions,
            supported_types,
        }
    }

    /// 使用 discriminator 识别账号类型
    pub fn identify_account_type_by_discriminator(&self, data: &[u8]) -> Option<AccountType> {
        if data.len() < 8 {
            return None;
        }

        let discriminator: [u8; 8] = data[0..8].try_into().ok()?;
        self.discriminator_map.get(&discriminator).cloned()
    }

    /// 解析账号数据
    pub fn parse_account_data(
        &self,
        address: Pubkey,
        account_type: AccountType,
        data: &[u8],
    ) -> Result<Box<dyn AccountData>> {
        if let Some(parse_fn) = self.parse_functions.get(&account_type) {
            parse_fn(self, address, data)
        } else {
            Err(shared::EchoesError::Parse(
                format!("No parse function found for account type: {:?}", account_type)
            ))
        }
    }
}

#[async_trait]
impl AccountParser for BaseAnchorParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![self.program_id]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        self.supported_types.clone()
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        *program_id == self.program_id &&
        self.identify_account_type_by_discriminator(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if *program_id != self.program_id {
            return None;
        }

        self.identify_account_type_by_discriminator(account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if program_id != self.program_id {
            return Err(shared::EchoesError::Parse(
                format!("Unsupported program ID: {}", program_id)
            ));
        }

        let account_type = self.identify_account_type_by_discriminator(account_data)
            .ok_or_else(|| shared::EchoesError::Parse(
                format!("Cannot identify account type for data length: {}", account_data.len())
            ))?;

        let parsed_data = self.parse_account_data(address, account_type.clone(), account_data)?;

        let parsed_account = ParsedAccount::new(
            address,
            program_id,
            account_type,
            parsed_data,
            account_data.len(),
        );

        // 验证解析结果
        self.validate_parsed_data(&parsed_account)?;

        Ok(parsed_account)
    }
}

/// 构建器模式用于创建 BaseAnchorParser
pub struct BaseAnchorParserBuilder {
    name: Option<String>,
    program_id: Option<Pubkey>,
    discriminator_map: DiscriminatorMap,
    parse_functions: HashMap<AccountType, ParseFunction>,
}

impl BaseAnchorParserBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            name: None,
            program_id: None,
            discriminator_map: HashMap::new(),
            parse_functions: HashMap::new(),
        }
    }

    /// 设置解析器名称
    pub fn name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 设置程序ID
    pub fn program_id(mut self, program_id: Pubkey) -> Self {
        self.program_id = Some(program_id);
        self
    }

    /// 添加账号类型映射
    pub fn add_account_type(
        mut self,
        discriminator: [u8; 8],
        account_type: AccountType,
        parse_fn: ParseFunction,
    ) -> Self {
        self.discriminator_map.insert(discriminator, account_type.clone());
        self.parse_functions.insert(account_type, parse_fn);
        self
    }

    /// 构建解析器
    pub fn build(self) -> Result<BaseAnchorParser> {
        let name = self.name.ok_or_else(|| {
            shared::EchoesError::Config("Parser name is required".to_string())
        })?;

        let program_id = self.program_id.ok_or_else(|| {
            shared::EchoesError::Config("Program ID is required".to_string())
        })?;

        Ok(BaseAnchorParser::new(
            name,
            program_id,
            self.discriminator_map,
            self.parse_functions,
        ))
    }
}

impl Default for BaseAnchorParserBuilder {
    fn default() -> Self {
        Self::new()
    }
}
