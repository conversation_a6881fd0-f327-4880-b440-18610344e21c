//! 通用适配器宏
//!
//! 提供宏来自动生成适配器结构体，减少重复代码

/// 生成 AccountData 适配器的宏
///
/// 这个宏自动生成适配器结构体和 AccountData trait 实现
///
/// # 参数
/// - `$adapter_name`: 适配器结构体名称
/// - `$inner_type`: 内部 Anchor 类型
/// - `$account_type`: AccountType 枚举值
/// - `$program_id`: 程序ID常量
/// - `$data_size`: 数据大小表达式
/// - `$json_fields`: JSON 序列化字段列表
/// - `$creator_field`: 可选的创建者字段
/// - `$status_field`: 可选的状态字段
/// - `$mints_fields`: 可选的关联 mint 字段列表
#[macro_export]
macro_rules! impl_account_data_adapter {
    (
        $adapter_name:ident,
        $inner_type:ty,
        $account_type:expr,
        $program_id:expr,
        $data_size:expr,
        json_fields: { $($field_name:ident: $field_expr:expr),* $(,)? }
        $(, creator: $creator_field:expr)?
        $(, status: $status_field:expr)?
        $(, mints: [$($mint_field:expr),* $(,)?])?
    ) => {
        #[derive(Debug)]
        pub struct $adapter_name {
            pub address: solana_sdk::pubkey::Pubkey,
            pub inner: $inner_type,
        }

        impl $crate::accounts::traits::AccountData for $adapter_name {
            fn account_type(&self) -> $crate::accounts::traits::AccountType {
                $account_type
            }

            fn address(&self) -> solana_sdk::pubkey::Pubkey {
                self.address
            }

            fn program_id(&self) -> solana_sdk::pubkey::Pubkey {
                $program_id
            }

            fn data_size(&self) -> usize {
                $data_size
            }

            fn to_json(&self) -> serde_json::Value {
                let inner = &self.inner;
                serde_json::json!({
                    "address": self.address.to_string(),
                    $(
                        stringify!($field_name): $field_expr,
                    )*
                })
            }

            $(
                fn creator(&self) -> Option<solana_sdk::pubkey::Pubkey> {
                    let inner = &self.inner;
                    Some($creator_field)
                }
            )?

            $(
                fn status(&self) -> Option<u8> {
                    let inner = &self.inner;
                    Some($status_field)
                }
            )?

            $(
                fn associated_mints(&self) -> Vec<solana_sdk::pubkey::Pubkey> {
                    let inner = &self.inner;
                    vec![$($mint_field),*]
                }
            )?
        }
    };
}

/// 生成解析函数的宏
///
/// 这个宏自动生成解析特定账号类型的函数
///
/// # 参数
/// - `$fn_name`: 函数名称
/// - `$anchor_type`: Anchor 类型
/// - `$adapter_type`: 适配器类型
#[macro_export]
macro_rules! impl_parse_function {
    ($fn_name:ident, $anchor_type:ty, $adapter_type:ty) => {
        pub fn $fn_name(
            _parser: &$crate::accounts::base::BaseAnchorParser,
            address: solana_sdk::pubkey::Pubkey,
            data: &[u8],
        ) -> shared::Result<Box<dyn $crate::accounts::traits::AccountData>> {
            let inner = <$anchor_type>::deserialize(&mut &data[8..])
                .map_err(|e| shared::EchoesError::Parse(
                    format!("Failed to deserialize {}: {}", stringify!($anchor_type), e)
                ))?;

            Ok(Box::new($adapter_type {
                address,
                inner,
            }))
        }
    };
}

/// 批量生成解析函数的宏
///
/// 这个宏可以一次性生成多个解析函数
#[macro_export]
macro_rules! impl_parse_functions {
    (
        $(
            $fn_name:ident => ($anchor_type:ty, $adapter_type:ty)
        ),* $(,)?
    ) => {
        $(
            $crate::impl_parse_function!($fn_name, $anchor_type, $adapter_type);
        )*
    };
}

/// 生成解析器构建器的宏
///
/// 这个宏自动生成解析器的构建逻辑
#[macro_export]
macro_rules! impl_parser_builder {
    (
        $parser_name:ident,
        $program_id:expr,
        discriminators: {
            $(
                $discriminator:expr => ($account_type:expr, $parse_fn:ident)
            ),* $(,)?
        }
    ) => {
        impl $parser_name {
            pub fn new() -> Self {
                use $crate::accounts::base::BaseAnchorParserBuilder;
                use std::collections::HashMap;

                let mut builder = BaseAnchorParserBuilder::new()
                    .name(stringify!($parser_name).to_string())
                    .program_id($program_id);

                $(
                    builder = builder.add_account_type(
                        $discriminator,
                        $account_type,
                        $parse_fn,
                    );
                )*

                Self {
                    inner: builder.build().expect("Failed to build parser"),
                }
            }
        }

        impl Default for $parser_name {
            fn default() -> Self {
                Self::new()
            }
        }

        #[async_trait::async_trait]
        impl $crate::accounts::traits::AccountParser for $parser_name {
            fn name(&self) -> &str {
                self.inner.name()
            }

            fn supported_program_ids(&self) -> Vec<solana_sdk::pubkey::Pubkey> {
                self.inner.supported_program_ids()
            }

            fn supported_account_types(&self) -> Vec<$crate::accounts::traits::AccountType> {
                self.inner.supported_account_types()
            }

            fn can_parse(&self, program_id: &solana_sdk::pubkey::Pubkey, account_data: &[u8]) -> bool {
                self.inner.can_parse(program_id, account_data)
            }

            fn identify_account_type(
                &self,
                program_id: &solana_sdk::pubkey::Pubkey,
                account_data: &[u8],
            ) -> Option<$crate::accounts::traits::AccountType> {
                self.inner.identify_account_type(program_id, account_data)
            }

            async fn parse_account(
                &self,
                address: solana_sdk::pubkey::Pubkey,
                program_id: solana_sdk::pubkey::Pubkey,
                account_data: &[u8],
            ) -> shared::Result<$crate::accounts::traits::ParsedAccount> {
                self.inner.parse_account(address, program_id, account_data).await
            }
        }
    };
}

/// 一体化解析器生成宏
///
/// 这个宏结合了所有功能，可以一次性生成完整的解析器
#[macro_export]
macro_rules! define_anchor_parser {
    (
        parser: $parser_name:ident,
        program_id: $program_id:expr,
        types: {
            $(
                $account_type:expr => {
                    discriminator: $discriminator:expr,
                    anchor_type: $anchor_type:ty,
                    adapter: $adapter_name:ident,
                    parse_fn: $parse_fn:ident,
                    data_size: $data_size:expr,
                    json_fields: { $($field_name:ident: $field_expr:expr),* $(,)? }
                    $(, creator: $creator_field:expr)?
                    $(, status: $status_field:expr)?
                    $(, mints: [$($mint_field:expr),* $(,)?])?
                }
            ),* $(,)?
        }
    ) => {
        // 生成适配器
        $(
            $crate::impl_account_data_adapter!(
                $adapter_name,
                $anchor_type,
                $account_type,
                $program_id,
                $data_size,
                json_fields: { $($field_name: $field_expr),* }
                $(, creator: $creator_field)?
                $(, status: $status_field)?
                $(, mints: [$($mint_field),*])?
            );
        )*

        // 生成解析函数
        $(
            $crate::impl_parse_function!($parse_fn, $anchor_type, $adapter_name);
        )*

        // 生成解析器结构体
        pub struct $parser_name {
            inner: $crate::accounts::base::BaseAnchorParser,
        }

        // 生成解析器实现
        $crate::impl_parser_builder!(
            $parser_name,
            $program_id,
            discriminators: {
                $(
                    $discriminator => ($account_type, $parse_fn)
                ),*
            }
        );
    };
}
