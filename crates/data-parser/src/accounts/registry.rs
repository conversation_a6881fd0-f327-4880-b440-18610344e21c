//! 账号解析器注册表
//!
//! 管理所有账号解析器的注册、查找和生命周期

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use shared::Result;
use solana_sdk::pubkey::Pubkey;
use crate::accounts::traits::{AccountParser, AccountType, AccountParseStats, ParsedAccount};
use crate::AnchorParserManager;

/// 账号解析器元数据
#[derive(Debug, Clone)]
pub struct AccountParserMetadata {
    /// 解析器名称
    pub name: String,
    /// 版本
    pub version: String,
    /// 支持的程序ID
    pub supported_programs: Vec<String>,
    /// 支持的账号类型
    pub supported_account_types: Vec<AccountType>,
    /// 优先级
    pub priority: u8,
    /// 描述
    pub description: String,
    /// 作者
    pub author: String,
}

/// 账号解析器注册表
///
/// 全局管理所有账号解析器的注册、查找和生命周期
pub struct AccountParserRegistry {
    /// 注册的解析器（解析器名称 -> 解析器实例）
    parsers: Arc<RwLock<HashMap<String, Arc<dyn AccountParser>>>>,
    /// 程序ID到解析器的映射（程序ID -> 解析器名称列表）
    program_to_parsers: Arc<RwLock<HashMap<String, Vec<String>>>>,
    /// 账号类型到解析器的映射（账号类型 -> 解析器名称列表）
    type_to_parsers: Arc<RwLock<HashMap<AccountType, Vec<String>>>>,
    /// 解析器统计信息
    stats: Arc<RwLock<HashMap<String, AccountParseStats>>>,
}

impl AccountParserRegistry {
    /// 创建新的账号解析器注册表
    pub fn new() -> Self {
        Self {
            parsers: Arc::new(RwLock::new(HashMap::new())),
            program_to_parsers: Arc::new(RwLock::new(HashMap::new())),
            type_to_parsers: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 注册解析器
    pub fn register_parser(&self, parser: Arc<dyn AccountParser>) -> Result<()> {
        let parser_name = parser.name().to_string();
        let program_ids = parser.supported_program_ids();
        let account_types = parser.supported_account_types();

        // 检查解析器是否已注册
        {
            let parsers = self.parsers.read().unwrap();
            if parsers.contains_key(&parser_name) {
                return Err(shared::EchoesError::Config(
                    format!("Account parser '{}' is already registered", parser_name)
                ));
            }
        }

        // 注册解析器
        {
            let mut parsers = self.parsers.write().unwrap();
            parsers.insert(parser_name.clone(), parser);
        }

        // 更新程序ID映射
        {
            let mut program_map = self.program_to_parsers.write().unwrap();
            for program_id in program_ids {
                program_map
                    .entry(program_id.to_string())
                    .or_insert_with(Vec::new)
                    .push(parser_name.clone());
            }
        }

        // 更新账号类型映射
        {
            let mut type_map = self.type_to_parsers.write().unwrap();
            for account_type in account_types {
                type_map
                    .entry(account_type)
                    .or_insert_with(Vec::new)
                    .push(parser_name.clone());
            }
        }

        // 初始化统计信息
        {
            let mut stats = self.stats.write().unwrap();
            stats.insert(parser_name.clone(), AccountParseStats::default());
        }

        tracing::info!("Registered account parser: {}", parser_name);
        Ok(())
    }

    /// 注销解析器
    pub fn unregister_parser(&self, parser_name: &str) -> Result<()> {
        // 获取要移除的解析器
        let parser = {
            let mut parsers = self.parsers.write().unwrap();
            parsers.remove(parser_name)
        };

        if let Some(parser) = parser {
            let program_ids = parser.supported_program_ids();
            let account_types = parser.supported_account_types();

            // 从程序ID映射中移除
            {
                let mut program_map = self.program_to_parsers.write().unwrap();
                for program_id in program_ids {
                    let program_id_str = program_id.to_string();
                    if let Some(parser_list) = program_map.get_mut(&program_id_str) {
                        parser_list.retain(|name| name != parser_name);
                        if parser_list.is_empty() {
                            program_map.remove(&program_id_str);
                        }
                    }
                }
            }

            // 从账号类型映射中移除
            {
                let mut type_map = self.type_to_parsers.write().unwrap();
                for account_type in account_types {
                    if let Some(parser_list) = type_map.get_mut(&account_type) {
                        parser_list.retain(|name| name != parser_name);
                        if parser_list.is_empty() {
                            type_map.remove(&account_type);
                        }
                    }
                }
            }

            // 移除统计信息
            {
                let mut stats = self.stats.write().unwrap();
                stats.remove(parser_name);
            }

            tracing::info!("Unregistered account parser: {}", parser_name);
            Ok(())
        } else {
            Err(shared::EchoesError::Config(
                format!("Account parser '{}' not found", parser_name)
            ))
        }
    }

    /// 根据程序ID查找解析器
    pub fn find_parsers_for_program(&self, program_id: &Pubkey) -> Vec<Arc<dyn AccountParser>> {
        let program_id_str = program_id.to_string();
        let program_map = self.program_to_parsers.read().unwrap();
        let parsers = self.parsers.read().unwrap();

        if let Some(parser_names) = program_map.get(&program_id_str) {
            parser_names
                .iter()
                .filter_map(|name| parsers.get(name).cloned())
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 根据账号类型查找解析器
    pub fn find_parsers_for_account_type(&self, account_type: &AccountType) -> Vec<Arc<dyn AccountParser>> {
        let type_map = self.type_to_parsers.read().unwrap();
        let parsers = self.parsers.read().unwrap();

        if let Some(parser_names) = type_map.get(account_type) {
            parser_names
                .iter()
                .filter_map(|name| parsers.get(name).cloned())
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 获取指定名称的解析器
    pub fn get_parser(&self, parser_name: &str) -> Option<Arc<dyn AccountParser>> {
        let parsers = self.parsers.read().unwrap();
        parsers.get(parser_name).cloned()
    }

    /// 获取所有注册的解析器
    pub fn get_all_parsers(&self) -> Vec<Arc<dyn AccountParser>> {
        let parsers = self.parsers.read().unwrap();
        parsers.values().cloned().collect()
    }

    /// 获取所有解析器的元数据
    pub fn get_parser_metadata(&self) -> Vec<AccountParserMetadata> {
        let parsers = self.parsers.read().unwrap();
        parsers
            .values()
            .map(|parser| {
                AccountParserMetadata {
                    name: parser.name().to_string(),
                    version: parser.version().to_string(),
                    supported_programs: parser.supported_program_ids().iter().map(|p| p.to_string()).collect(),
                    supported_account_types: parser.supported_account_types(),
                    priority: parser.priority(),
                    description: format!("{} 账号数据解析器", parser.name()),
                    author: "Echoes Team".to_string(),
                }
            })
            .collect()
    }

    /// 获取解析器统计信息
    pub fn get_stats(&self) -> HashMap<String, AccountParseStats> {
        let stats = self.stats.read().unwrap();
        stats.clone()
    }

    /// 获取特定解析器的统计信息
    pub fn get_parser_stats(&self, parser_name: &str) -> Option<AccountParseStats> {
        let stats = self.stats.read().unwrap();
        stats.get(parser_name).cloned()
    }

    /// 更新解析器统计信息
    pub fn update_parser_stats<F>(&self, parser_name: &str, update_fn: F)
    where
        F: FnOnce(&mut AccountParseStats),
    {
        let mut stats = self.stats.write().unwrap();
        if let Some(parser_stats) = stats.get_mut(parser_name) {
            update_fn(parser_stats);
        }
    }

    /// 重置所有统计信息
    pub fn reset_all_stats(&self) {
        let mut stats = self.stats.write().unwrap();
        for stat in stats.values_mut() {
            *stat = AccountParseStats::default();
        }
    }

    /// 获取支持的程序ID列表
    pub fn get_supported_program_ids(&self) -> Vec<Pubkey> {
        let program_map = self.program_to_parsers.read().unwrap();
        program_map.keys()
            .filter_map(|s| s.parse().ok())
            .collect()
    }

    /// 获取支持的账号类型列表
    pub fn get_supported_account_types(&self) -> Vec<AccountType> {
        let type_map = self.type_to_parsers.read().unwrap();
        type_map.keys().cloned().collect()
    }

    /// 检查程序ID是否被支持
    pub fn is_program_supported(&self, program_id: &Pubkey) -> bool {
        let program_id_str = program_id.to_string();
        let program_map = self.program_to_parsers.read().unwrap();
        program_map.contains_key(&program_id_str)
    }

    /// 检查账号类型是否被支持
    pub fn is_account_type_supported(&self, account_type: &AccountType) -> bool {
        let type_map = self.type_to_parsers.read().unwrap();
        type_map.contains_key(account_type)
    }

    /// 获取注册的解析器数量
    pub fn parser_count(&self) -> usize {
        let parsers = self.parsers.read().unwrap();
        parsers.len()
    }

    /// 解析账号数据（使用最佳匹配的解析器）
    pub async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        let parsers = self.find_parsers_for_program(&program_id);

        if parsers.is_empty() {
            return Err(shared::EchoesError::Parse(
                format!("No parser found for program ID: {}", program_id)
            ));
        }

        // 尝试每个解析器，返回第一个成功的结果
        let mut last_error = None;
        for parser in parsers {
            let start_time = std::time::Instant::now();

            match parser.parse_account(address, program_id, account_data).await {
                Ok(parsed_account) => {
                    let duration = start_time.elapsed().as_millis() as u64;

                    // 更新统计信息
                    let account_type = parsed_account.account_type.clone();
                    self.update_parser_stats(parser.name(), |stats| {
                        stats.record_success(account_type, duration);
                    });

                    return Ok(parsed_account);
                }
                Err(e) => {
                    let duration = start_time.elapsed().as_millis() as u64;

                    // 更新失败统计
                    self.update_parser_stats(parser.name(), |stats| {
                        stats.record_failure(duration);
                    });

                    last_error = Some(e);
                }
            }
        }

        Err(last_error.unwrap_or_else(|| {
            shared::EchoesError::Parse("All parsers failed".to_string())
        }))
    }

    /// 批量解析账号数据
    pub async fn parse_accounts(
        &self,
        accounts: Vec<(Pubkey, Pubkey, Vec<u8>)>, // (address, program_id, data)
    ) -> Vec<Result<ParsedAccount>> {
        let mut results = Vec::with_capacity(accounts.len());

        for (address, program_id, data) in accounts {
            let result = self.parse_account(address, program_id, &data).await;
            results.push(result);
        }

        results
    }

    /// 健康检查所有解析器
    pub async fn health_check_all(&self) -> HashMap<String, bool> {
        let parsers = self.get_all_parsers();
        let mut results = HashMap::new();

        for parser in parsers {
            let name = parser.name().to_string();
            let is_healthy = parser.health_check().await.is_ok();
            results.insert(name, is_healthy);
        }

        results
    }
}

impl Default for AccountParserRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局账号解析器注册表实例
static GLOBAL_ACCOUNT_REGISTRY: std::sync::OnceLock<AccountParserRegistry> = std::sync::OnceLock::new();

/// 获取全局账号解析器注册表
pub fn global_account_registry() -> &'static AccountParserRegistry {
    GLOBAL_ACCOUNT_REGISTRY.get_or_init(AccountParserRegistry::new)
}

/// 注册解析器到全局注册表
pub fn register_global_account_parser(parser: Arc<dyn AccountParser>) -> Result<()> {
    global_account_registry().register_parser(parser)
}

/// 从全局注册表查找程序解析器
pub fn find_global_parsers_for_program(program_id: &Pubkey) -> Vec<Arc<dyn AccountParser>> {
    global_account_registry().find_parsers_for_program(program_id)
}

/// 从全局注册表查找账号类型解析器
pub fn find_global_parsers_for_account_type(account_type: &AccountType) -> Vec<Arc<dyn AccountParser>> {
    global_account_registry().find_parsers_for_account_type(account_type)
}

/// 初始化并注册所有 Anchor 解析器
pub fn initialize_anchor_parsers() -> Result<()> {
    use crate::accounts::manager::AnchorParserManager;

    // 注册 Anchor 解析器管理器
    let anchor_manager = Arc::new(AnchorParserManager::new());
    register_global_account_parser(anchor_manager)?;

    tracing::info!("Successfully registered Anchor parsers");
    Ok(())
}

/// 初始化所有解析器（包括传统解析器和 Anchor 解析器）
pub fn initialize_all_parsers() -> Result<()> {
    // 注册 Anchor 解析器
    initialize_anchor_parsers()?;

    tracing::info!("Successfully initialized all account parsers");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use shared::Result;
    use crate::accounts::traits::{AccountType, ParsedAccount, AccountData};

    // 测试用解析器
    struct TestAccountParser {
        name: String,
        program_ids: Vec<Pubkey>,
        account_types: Vec<AccountType>,
    }

    impl TestAccountParser {
        fn new(name: &str, program_ids: Vec<&str>, account_types: Vec<AccountType>) -> Self {
            Self {
                name: name.to_string(),
                program_ids: program_ids.into_iter().map(|s| s.parse().expect("Invalid Pubkey")).collect(),
                account_types,
            }
        }

        fn new_with_pubkeys(name: &str, program_ids: Vec<Pubkey>, account_types: Vec<AccountType>) -> Self {
            Self {
                name: name.to_string(),
                program_ids,
                account_types,
            }
        }
    }

    // 测试用账号数据
    #[derive(Debug)]
    struct TestAccountData {
        address: Pubkey,
        account_type: AccountType,
    }

    impl AccountData for TestAccountData {
        fn account_type(&self) -> AccountType {
            self.account_type.clone()
        }

        fn address(&self) -> Pubkey {
            self.address
        }

        fn program_id(&self) -> Pubkey {
            Pubkey::default()
        }

        fn data_size(&self) -> usize {
            100
        }

        fn to_json(&self) -> serde_json::Value {
            serde_json::Value::Null
        }
    }

    #[async_trait]
    impl AccountParser for TestAccountParser {
        fn name(&self) -> &str {
            &self.name
        }

        fn supported_program_ids(&self) -> Vec<Pubkey> {
            self.program_ids.clone()
        }

        fn supported_account_types(&self) -> Vec<AccountType> {
            self.account_types.clone()
        }

        fn can_parse(&self, program_id: &Pubkey, _account_data: &[u8]) -> bool {
            self.program_ids.contains(program_id)
        }

        fn identify_account_type(&self, program_id: &Pubkey, _account_data: &[u8]) -> Option<AccountType> {
            if self.can_parse(program_id, _account_data) {
                self.account_types.first().cloned()
            } else {
                None
            }
        }

        async fn parse_account(
            &self,
            address: Pubkey,
            program_id: Pubkey,
            _account_data: &[u8],
        ) -> Result<ParsedAccount> {
            let account_type = self.account_types.first().cloned().unwrap_or(AccountType::Unknown);
            let data = Box::new(TestAccountData { address, account_type: account_type.clone() });

            Ok(ParsedAccount::new(
                address,
                program_id,
                account_type,
                data,
                100,
            ))
        }
    }

    #[tokio::test]
    async fn test_account_parser_registry() {
        let registry = AccountParserRegistry::new();

        // 测试注册解析器
        let program_id1 = Pubkey::new_unique();
        let _program_id2 = Pubkey::new_unique();

        let parser1 = Arc::new(TestAccountParser::new_with_pubkeys(
            "test1",
            vec![program_id1],
            vec![AccountType::RaydiumPoolState],
        ));
        assert!(registry.register_parser(parser1.clone()).is_ok());

        // 测试重复注册
        let parser1_dup = Arc::new(TestAccountParser::new_with_pubkeys(
            "test1",
            vec![Pubkey::new_unique()],
            vec![AccountType::MeteoraLbPair],
        ));
        assert!(registry.register_parser(parser1_dup).is_err());

        // 测试查找解析器
        let found_parsers = registry.find_parsers_for_program(&program_id1);
        assert_eq!(found_parsers.len(), 1);
        assert_eq!(found_parsers[0].name(), "test1");

        // 测试按账号类型查找
        let found_by_type = registry.find_parsers_for_account_type(&AccountType::RaydiumPoolState);
        assert_eq!(found_by_type.len(), 1);
        assert_eq!(found_by_type[0].name(), "test1");

        // 测试解析账号
        let address = Pubkey::new_unique();
        let account_data = vec![0u8; 100];
        let result = registry.parse_account(address, program_id1, &account_data).await;
        assert!(result.is_ok());

        // 测试注销解析器
        assert!(registry.unregister_parser("test1").is_ok());
        assert!(registry.unregister_parser("test1").is_err());

        // 验证注销后查找不到
        let found_parsers = registry.find_parsers_for_program(&program_id1);
        assert_eq!(found_parsers.len(), 0);
    }
}
