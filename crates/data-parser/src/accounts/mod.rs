//! 账号数据解析模块
//!
//! 提供对不同 DEX 账号数据的结构化解析能力

pub mod traits;
pub mod registry;
pub mod raydium;
pub mod meteora;

#[cfg(test)]
mod anchor_integration_test;
mod manager;

pub use traits::{AccountParser, AccountData, AccountType, ParsedAccount};
pub use registry::{AccountParserRegistry, AccountParserMetadata, global_account_registry};
pub use raydium::{RaydiumAnchorAccountParser};
pub use meteora::{MeteoraAnchorAccountParser};
pub use manager::{An<PERSON><PERSON>arserManager, create_anchor_parser_manager};
